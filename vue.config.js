'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')

function resolve(dir) {
    return path.join(__dirname, dir)
}

const name = defaultSettings.title || '' // page title
const webpack = require("webpack");

const port = process.env.port || process.env.npm_config_port || 9528 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
    publicPath: './',
    outputDir: path.resolve(__dirname, './dist'),
    // outputDir: 'dist',
    assetsDir: 'static',
    lintOnSave: false,
    productionSourceMap: true,
    devServer: {
        port: port,
        proxy: {
            "/api/": {
                // target: process.env.VUE_APP_PROXY_URL,
                target: 'http://meproduct.test.ybm100.com',
                changeOrigin: true,
                onProxyReq(proxyReq) {
                    // let tempCookies = proxyReq.getHeader('cookie');
                    // 手动设置cookie
                    // if (!tempCookies) {
                    //   delete require.cache[require.resolve("./cookies.local.js")];
                    //   const cookies = require("./cookies.local.js").cookies;
                    //   cookies && proxyReq.setHeader("cookie", cookies);
                    // }
                    // delete require.cache[require.resolve("./cookies.local.js")];
                   // const cookies = "uid=CgoUFGX6SlBuCKukBe+lAg==; crosSdkDT2019DeviceId=-vd8gwc--yidxgw-hv9gd4o4yrbmkiy-oq6ngmr91; web_dc504156a0b54a04bd19c57da5422a32=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; web_cdaeb9bc6854423b9ab74b1827bc21a0=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFIM%E5%B7%A5%E5%8D%95%E7%B3%BB%E7%BB%9F%5C%22%7D%22%7D; web_8b5e1b0f250a436e8c6af9871354bfba=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; ssxmod_itna=YqRx070=0QiQD=G8QDXKDHQyx62D9l7QY/lZciZDGT5Ds=rWDSxGKidDqxBec=kpxwIeo+Lodj7GPjxLQB8irAiiB48pWDU4i8DCkxvKCDentD5xGoDPxDeDAiKiTDY4Dd6vTX=DEDeKDmxiODlIHXxDaRxi361KDRg5D0aqHDQKDuxvxDG54Yn05djbwn2cw48oDn6D1=nikD75IDlaen8ikbFM6KG516/S+UCKhe40OD0IG6xibzOgP0Ey+FliWNiri67Wqtj4x+jiYWWex=i04rm1eqlhWhtb58NXxDDpeFnhDD==; ssxmod_itna2=YqRx070=0QiQD=G8QDXKDHQyx62D9l7QY/lZciZDGqik6==rDlalDjRR001Q1u=qyYZw5GF+mkr6kKx+SuQK=ZCdx6xarUYywOie8lxUPzV0cYopsEUKAiWuF2LxX1=/8BTNGd/xgIYBOhpFoo1S8IPFgKRF+0GFkP483hevdvCWBd54mQ80lxUYCKrkmbbpE2/F4AK7jQP5495Mg0pBx5rIoohFYfZU1KwFOXaCC7OBSfe=8xW6mnnST7wACDsHRoGKY8XCbAK9TSf+FkaPLO31RlmE+FyTklXrLhrNXErV/Y1c/YU=0b4qiz0t8xDKwiLAahLQBB3y8hs2atn3rYoMBxD7=DYIxeD=; web_did=%7B%22did%22%3A%20%222d88a523-589c-403d-83d5-aa700dab3d25%22%7D; web_info=%7B%22sid%22%3A%201736422373333%2C%22updated%22%3A%201736422373334%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22new-www.test.ybm100.com%22%2C%22cuid%22%3A%20%220%22%7D; sid=80ae0f88-fbe8-4feb-a0e2-8bc3d3950ef0; TGC=eyJhbGciOiJIUzUxMiJ9.ZXlKNmFYQWlPaUpFUlVZaUxDSmhiR2NpT2lKa2FYSWlMQ0psYm1NaU9pSkJNVEk0UTBKRExVaFRNalUySW4wLi5Zemh1TkhpZmx6RGd4Q3VXaVNhaERRLmdaREJpU3ZJajMxQmpBQVdlWktPYXdnQU1QM1paWEp2dUloSWJqaWltNGR3M0dmQ0w2SXVIRXpnMnZlTURKWktybW9jYUotMkpuSGhCX01fWHJ4RVJVYzNUTmtMQTY1R25ick9kV2lvX0pSemdFaGQzbWFySE1Ld0NFWWdvaGZzbEkwbk5KM19xb2xua1FFOGx2bzcwUWJGRW9NSXVmQ2pQdXV1c1hyVUVnYzNpaXJCUTBPdkZJS05jbFlaekt5Nll3M2x4UWVxMWo3QjhJRTMtc1NyUkhGaHlzcDd5emowb050RGJjbllORElGVm1WS08tbnRHSG1sQ1lMTWQ2N0VQcXF2MVVzRHN6VkE2NDZOeUlrbEdnLmlSRXh2VEdMYlFCYlk2d2JDQzUzR0E=.KCsuhXu7NzHgZDYto8tIl_2QXnary-t-lrPlSRckb0YyJeLzax6HNX_BofOm1GU12XJlpN5dp7WT666BFCvOaw";
                    const cookies = "uid=rBQRYGgJ/fZCrTs4BoRFAg==; web_dc504156a0b54a04bd19c57da5422a32=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; web_8b5e1b0f250a436e8c6af9871354bfba=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; crosSdkDT2019DeviceId=-gc0ooj--g4wuqq-xj1epyonqmiuth2-0jeecnz31; web_did=%7B%22did%22%3A%20%223ab460f3-e9e9-4973-8f0e-2cae40e4a240%22%7D; isDeviceUpload=1; web_info=%7B%22sid%22%3A%201751450730478%2C%22updated%22%3A%201751450730479%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22pop.test.ybm100.com%22%2C%22cuid%22%3A%20%22SH10001226%22%7D; TGC=eyJhbGciOiJIUzUxMiJ9.ZXlKNmFYQWlPaUpFUlVZaUxDSmhiR2NpT2lKa2FYSWlMQ0psYm1NaU9pSkJNVEk0UTBKRExVaFRNalUySW4wLi45TWNPaC1SZWg5MGlYZjJfTU9YYmlBLk9tVFA2NnlfSDZ5YWYxdFJCajlvVkFsMzhXYThnem9FNGFzemRSN0RSaE1Md0FVNHk0VW0yNF9HTXNVLVBqV3MtdGVuWkgySWZHUkNKRTdtMlZjVkRqdXd2c296NGtqX09JT2hyOG01NHhyd2RfZFpYSjFGelRqdElRZldpdGJJZTFPWlFmT0FrN05iWGxmVWRzRzhSQjB3TWowbEUwVndsODd5dmpmZk9lM1dNTTYzVi1xUjZvQVJ4eGRrMHo3ZWdQa2IyWHRRX2VQQmE3WWE2SmxzMVVBSzFoWnRXaGkwUjQ3QkdCRS1xNFJGenZRdWk5OFRJc1lRRlp1V2JxdExVVDFFYmc5Y1JzeXR1OTRFSmc2WGFnLjB2dFhPRzlQeXkyYndRejA3eTJxRVE=.3NEWbE-PAXsXtorwfkjQq731I2XT_mkh0Sewry4ZBRwB9flqrLBh0IYU49H8ray9GLXNeXKJ4Q_crvNs5DR2fA; sid=ad783475-9f9a-4cb2-bd4d-ec7f6ddace72";
                    //  const cookies = "uid=rBQRYGesDMU+ogLfAyUzAg==; sid=8c960bee-f1a6-4778-a234-a73411e83d52; TGC=eyJhbGciOiJIUzUxMiJ9.ZXlKNmFYQWlPaUpFUlVZaUxDSmhiR2NpT2lKa2FYSWlMQ0psYm1NaU9pSkJNVEk0UTBKRExVaFRNalUySW4wLi5YeXJFWjBNYld4R3ZXTnFBeV82Ty1BLkhLMXU2cGVWSnZNZXBWR3hULWVxajRGT2RKUDRveXdJeDE1Z2w2eDl1VXVvTGJqcURPenB6amdQUlNPNEZfY1o3bFZ5TUFUbEdOZG5lRVFyaHFzN1RvUVg4LU00b1RjTkJCYW02LTJoblNDa2ZLOUw1RDRIbnZFZ28tRGdydm9Nb3dxTmU2MHVjSWpod2JhNVlnTm50RVdrdjNDNVl2MWNqRFRhMFI1dlBOdVFjME53VVgxNV9jeTZDb25sa2VKSGVmenZLLTljWEhpUS1TQVd2QmgwTTJ6QXhKNVpZcVgtTkJ1QXJBV1lra0ZFOWR2R0hUV0Jua1o2UlJuMTFidEh2aUJQMmlUbVQtWjg3d3Y1cWpZUWh3LlZmSHlTNmpuYjhXZktlWDFBVXd0aXc=.YWNxJ1CIt_ioT-0iRpwWLsx1xEpB4scnorgeShPnFTm4cMg4S2eaJqYuF60zd7uOqWmA57L5JmsqWnFzdH2zHw"
                    cookies && proxyReq.setHeader("cookie", cookies);

                    proxyReq.setHeader("cache-control", "max-age=0");
                    proxyReq.setHeader(
                        "referer",
                        `http://meproduct.test.ybm100.com`
                    );
                    proxyReq.setHeader("sec-fetch-dest", "document");
                    proxyReq.setHeader("sec-fetch-mode", "navigate");
                    proxyReq.setHeader("sec-fetch-site", "none");
                    proxyReq.setHeader("sec-fetch-user", "?1");
                    proxyReq.setHeader("x-forwarded-host", process.env.VUE_APP_PROXY_URL);
                    proxyReq.setHeader("x-forwarded-for", process.env.VUE_APP_PROXY_URL);
                },
            },
            "/ocr/": {
                target: process.env.VUE_APP_OCR_API_URL || 'https://xyy-ocr.test.ybm100.com',
                changeOrigin: true,
                secure: true,
                onProxyReq(proxyReq) {
                    proxyReq.setHeader("cache-control", "max-age=0");
                },
            }
        },
    },
    configureWebpack: {
        name: name,
        resolve: {
            alias: {
                '@': resolve('src')
            }
        },
        externals: {
            'vue': 'Vue',
            'element-ui': 'ELEMENT'
        },
        devtool: 'source-map',
        //支持jquery
        plugins: [
            new webpack.ProvidePlugin({
                $: "jquery",
                jQuery: "jquery",
                "windows.jQuery": "jquery"
            })
        ]
    },
    chainWebpack(config) {
        config.plugins.delete('preload') //  need test
        config.plugins.delete('prefetch') //  need test

        // set svg-sprite-loader
        config.module
            .rule('svg')
            .exclude.add(resolve('src/icons'))
            .end()
        config.module
            .rule('icons')
            .test(/\.svg$/)
            .include.add(resolve('src/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
                symbolId: 'icon-[name]'
            })
            .end()

        // set preserveWhitespace
        config.module
            .rule('vue')
            .use('vue-loader')
            .loader('vue-loader')
            .tap(options => {
                options.compilerOptions.preserveWhitespace = true
                return options
            })
            .end()

        config
        // https://webpack.js.org/configuration/devtool/#development
            .when(process.env.NODE_ENV === 'development',
            config => config.devtool('cheap-source-map')
        )

        config
            .when(process.env.NODE_ENV !== 'development',
                config => {
                    config
                        .plugin('ScriptExtHtmlWebpackPlugin')
                        .after('html')
                        .use('script-ext-html-webpack-plugin', [{
                            // `runtime` must same as runtimeChunk name. default is `runtime`
                            inline: /runtime\..*\.js$/
                        }])
                        .end()
                    config
                        .optimization.splitChunks({
                            chunks: 'all',
                            cacheGroups: {
                                libs: {
                                    name: 'chunk-libs',
                                    test: /[\\/]node_modules[\\/]/,
                                    priority: 10,
                                    chunks: 'initial' // only package third parties that are initially dependent
                                },
                                commons: {
                                    name: 'chunk-commons',
                                    test: resolve('src/components'), // can customize your rules
                                    minChunks: 3, //  minimum common number
                                    priority: 5,
                                    reuseExistingChunk: true
                                }
                            }
                        })
                    config.optimization.runtimeChunk('single')
                }
            )
    }
}
