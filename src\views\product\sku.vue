
<template>
  <!-- v-loading="!skuLoading" -->
  <div class="sku-container">
    <div v-show="!isGIFT">
      <div class="sku-title" @click="showForm = !showForm">
        SKU
        <i style="color: #3b95a8" :class="showForm ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
        <el-popover placement="top" width="200" trigger="hover" :content="$store.getters.info[1] ? $store.getters.info[1].showContent : ''">
          <i class="info-suffix-icon el-icon-question" slot="reference"></i>
        </el-popover>
        <div class="tip" v-if="skuData.length > 2 || (operationType == 'reuse' && $route.query.page != 'spu')" @click.stop="handlMoreSku">
          查看关联sku({{ skuData.length }})
        </div>
      </div>
      <div v-show="showForm && $route.query.page != 'spu'" class="form-wrap">
        <el-form
          :disabled="(formDisable && !formAuth.length) || disableStatus"
          ref="skuRef"
          :model="skuForm"
          :rules="skuFormRules"
          class="sku-form"
        >
          <el-row type="flex">
            <!-- sku编码 -->
            <el-col :md="8" :sm="24" :xs="24">
              <el-form-item
                label="SKU编码"
                prop="skuCode"
                :class="{
                  'is-change': changeList.includes('skuCode') || coorectionType['skuCode'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['skuCode']"
                    :disabled="!coorectionType['skuCode']"
                    placement="top-start"
                  >
                    <span>SKU编码</span>
                  </el-tooltip>
                </template>
                <div class="copy-input-container">
                  <div class="copy-input">
                    <el-input v-model="skuForm.skuCode" :disabled="true" placeholder="" maxlength="100" size="small"></el-input>
                  </div>
                  <div class="copy-btn">
                    <span  class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.skuCode)">复制</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <!-- 标准库ID -->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                label="标准库ID"
                prop="productId"
                :class="{
                  'is-change': changeList.includes('productId') || coorectionType['productId'],
                }"
                >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['productId']"
                    :disabled="!coorectionType['productId']"
                    placement="top"
                  >
                    <span>标准库ID</span>
                  </el-tooltip>
                </template>
                <div class="copy-input-container">
                  <div class="copy-input">
                      <el-input v-model="skuForm.productId" :disabled="true" placeholder="" maxlength="100" size="small"></el-input>
                  </div>
                  <div class="copy-btn">
                    <span  class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.productId)">复制</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <!-- 预首营状态 -->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                label="自营状态"
                prop="preOperateStatusName"
                :class="{
                  'is-change': changeList.includes('preOperateStatusName') || coorectionType['preOperateStatusName'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['preOperateStatusName']"
                    :disabled="!coorectionType['preOperateStatusName']"
                    placement="top-start"
                  >
                    <span>预首营状态</span>
                  </el-tooltip>
                </template>
                <el-input v-model="skuForm.preOperateStatusName" disabled size="small"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <!-- 商品名 -->
            <el-col :md="8" :sm="24" :xs="24">
              <el-form-item
                label="商品名"
                prop="skuName"
                :class="{
                  'is-change': changeList.includes('skuName') || coorectionType['skuName'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['skuName']"
                    :disabled="!coorectionType['skuName']"
                    placement="top-start"
                  >
                    <span>商品名</span>
                  </el-tooltip>
                </template>
                <div class="copy-input-container">
                  <div class="copy-input">
                    <el-input
                      v-model="skuForm.skuName"
                      :disabled="checkOperatePermission('skuName') || formDisable"
                      placeholder="最多输入100字符"
                      maxlength="100"
                      @blur="formatskuName"
                      size="small"
                    ></el-input>
                  </div>
                  <div class="copy-btn">
                    <span  class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.skuName)">复制</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <!-- 品牌/商标 -->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                label="品牌/商标"
                prop="brand"
                :class="{
                  'is-change': changeList.includes('brand') || coorectionType['brand'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['brand']"
                    :disabled="!coorectionType['brand']"
                    placement="top-start"
                  >
                    <span>品牌/商标</span>
                  </el-tooltip>
                </template>
                <div class="copy-input-container">
                  <div class="copy-input">
                      <el-input v-model="skuForm.brand" :disabled="checkOperatePermission('brand') || formDisable" size="small"></el-input>
                  </div>
                  <div class="copy-btn">
                    <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.brand)">复制</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <!-- 品牌分类 -->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                label="品牌分类"
                prop="brandCategory"
                :class="{
                  'is-change': changeList.includes('brandCategory') || coorectionType['brandCategory'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['brandCategory']"
                    :disabled="!coorectionType['brandCategory']"
                    placement="top-start"
                  >
                    <span>品牌分类</span>
                  </el-tooltip>
                </template>
                <el-select
                  v-model="skuForm.brandCategory"
                  placeholder="请选择"
                  size="small"
                  :disabled="checkOperatePermission('brandCategory') || formDisable"
                >
                  <el-option
                    v-for="item in $store.getters.selectOptions.brandCategoryOptions"
                    :key="'key_brandCategoryOptions_' + item.id"
                    :label="item.dictName"
                    :value="item.id"
                    :disabled="!item.isValid"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :md="4" :sm="12" :xs="24" v-if="showUnitFirstClass">
              <el-form-item
                label="包装单位"
                prop="packageUnit"
                :class="{
                  'is-change': changeList.includes('packageUnit') || coorectionType['packageUnit'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['packageUnit']"
                    :disabled="!coorectionType['packageUnit']"
                    placement="top-start"
                  >
                    <span>包装单位</span>
                  </el-tooltip>
                </template>
                <el-select
                  v-model="skuForm.packageUnit"
                  placeholder="请选择"
                  :disabled="(checkOperatePermission('packageUnit') || formDisable) && formAuth.indexOf('packageUnit') == -1"
                  size="small"
                >
                  <el-option
                    v-for="item in $store.getters.selectOptions.packageUnitOptions"
                    :key="'key_packageUnitOptions_' + item.id"
                    :label="item.dictName"
                    :value="item.id"
                    :disabled="!item.isValid"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 净含量 -->
            <el-col :md="4" :sm="12" :xs="24" v-if="showUnitFirstClass">
              <el-form-item
                :class="{
                  'is-change': changeList.includes('netContent') || coorectionType['netContent'],
                }"
                prop="netContent"
                :rules="skuFormOptions.netContent"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['netContent']"
                    :disabled="!coorectionType['netContent']"
                    placement="top-start"
                  >
                    <span>每{{ unitLabel }}净含量</span>
                  </el-tooltip>
                </template>
                <div class="input-with-select">
                  <el-input
                    v-model="skuForm.netContent"
                    @blur="validateNetContentFields"
                    style="width: 60px"
                    placeholder="请输入"
                    size="small"
                    :disabled="(checkOperatePermission('netContent') || formDisable) && formAuth.indexOf('netContent') == -1"
                  ></el-input>
                  <el-form-item 
                    prop="netContentUnit"
                    :rules="skuFormOptions.netContentUnit">
                    <el-select 
                      v-model="skuForm.netContentUnit"
                      style="width: 60px"
                      size="small"
                      @change="(val) => {packageUnitChange(val,'netContentUnit');validateNetContentFields()}"
                      :disabled="(checkOperatePermission('netContent') || formDisable) && formAuth.indexOf('netContent') == -1"
                    >
                      <el-option
                        v-for="item in netContentUnitList"
                        :key="item.id"
                        :value="item.id"
                        :label="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </el-form-item>
            </el-col>
            <!-- 规格型号 -->
            <el-col :md="8" :sm="24" :xs="24" v-else>
              <el-form-item
                label="规格型号"
                prop="spec"
                :class="{
                  'is-change': changeList.includes('spec') || coorectionType['spec'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['spec']"
                    :disabled="!coorectionType['spec']"
                    placement="top-start"
                  >
                    <span>规格型号</span>
                  </el-tooltip>
                </template>
                <div class="copy-input-container">
                  <div class="copy-input">
                    <el-input
                      v-model="skuForm.spec"
                      :disabled="checkOperatePermission('spec') || formDisable"
                      @input="widthCheck(skuForm.spec, 40)"
                      @blur="specBlur"
                      size="small"
                    ></el-input>
                  </div>
                  <div class="copy-btn">
                    <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.spec)">复制</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <!-- DI码 -->
            <el-col :md="4" :sm="12" :xs="24"  v-if="spuCategory.id == 3">
              <el-form-item
                label="DI"
                prop="di"
                :class="{
                  'is-change': changeList.includes('di') || coorectionType['di'],
                }"
               
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['di']"
                    :disabled="!coorectionType['di']"
                    placement="top-start"
                  >
                    <span>DI</span>
                  </el-tooltip>
                </template>
                <el-input
                  v-model="skuForm.di"
                  maxlength="255"
                  :disabled="checkOperatePermission('di') || formDisable"
                  size="small"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <!-- DI码 -->
            <el-col :md="4" :sm="12" :xs="24">
              <el-form-item
                label="口味"
                prop="tasteName"
                :class="{
                  'is-change': changeList.includes('tasteName') || coorectionType['tasteName'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['tasteName']"
                    :disabled="!coorectionType['tasteName']"
                    placement="top-start"
                  >
                    <span>口味</span>
                  </el-tooltip>
                </template>
                <el-input
                  v-model="skuForm.tasteName"
                  maxlength="255"
                  :disabled="(operationType == 'operate' || formDisable) && formAuth.indexOf('tasteName') == -1"
                  size="small"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 尺码 -->
            <el-col :md="4" :sm="12" :xs="24">
              <el-form-item
                label="尺码"
                prop="sizeName"
                :class="{
                  'is-change': changeList.includes('sizeName') || coorectionType['sizeName'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['sizeName']"
                    :disabled="!coorectionType['sizeName']"
                    placement="top-start"
                  >
                    <span>尺码</span>
                  </el-tooltip>
                </template>
                <el-input
                  v-model="skuForm.sizeName"
                  maxlength="255"
                  :disabled="(operationType == 'operate' || formDisable) && formAuth.indexOf('sizeName') == -1"
                  size="small"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 颜色 -->
            <el-col :md="4" :sm="12" :xs="24" >
              <el-form-item
                label="颜色"
                prop="colorName"
                :class="{
                  'is-change': changeList.includes('colorName') || coorectionType['colorName'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['colorName']"
                    :disabled="!coorectionType['colorName']"
                    placement="top-start"
                  >
                    <span>颜色</span>
                  </el-tooltip>
                </template>
                <el-input
                  v-model="skuForm.colorName"
                  maxlength="255"
                  :disabled="(operationType == 'operate' || formDisable) && formAuth.indexOf('colorName') == -1"
                  size="small"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <!-- 包装规格 -->
            <el-col :md="8" :sm="12" :xs="24" v-if="showUnitFirstClass">
              <el-form-item
                prop="spec"
                :class="{
                  'is-change': changeList.includes('pageSpec') || coorectionType['pageSpec'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                      class="item"
                      effect="dark"
                      :content="coorectionType['pageSpec']"
                      :disabled="!coorectionType['pageSpec']"
                      placement="top-start"
                    >
                      <span>包装规格</span>
                    </el-tooltip>
                </template>
                <el-input
                  v-model="skuForm.spec"
                  style="width: 100%"
                  placeholder="请输入数量"
                  size="small"
                  @blur="specBlur"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- 包装单位 -->
            <el-col :md="8" :sm="12" :xs="24" v-if="!showUnitFirstClass">
              <el-form-item
                label="包装单位"
                prop="packageUnit"
                :class="{
                  'is-change': changeList.includes('packageUnit') || coorectionType['packageUnit'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['packageUnit']"
                    :disabled="!coorectionType['packageUnit']"
                    placement="top-start"
                  >
                    <span>包装单位</span>
                  </el-tooltip>
                </template>
                <el-select
                  v-model="skuForm.packageUnit"
                  placeholder="请选择"
                  :disabled="(checkOperatePermission('packageUnit') || formDisable) && formAuth.indexOf('packageUnit') == -1"
                  size="small"
                >
                  <el-option
                    v-for="item in $store.getters.selectOptions.packageUnitOptions"
                    :key="'key_packageUnitOptions_' + item.id"
                    :label="item.dictName"
                    :value="item.id"
                    :disabled="!item.isValid"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!--存储条件-->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                :prop="hasStorageCond()"
                ref="storageCond"
                :class="{
                  'is-change': changeList.includes('storageCond') || coorectionType['storageCond'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['storageCond']"
                    :disabled="!coorectionType['storageCond']"
                    placement="top-start"
                  >
                    <span>存储条件</span>
                  </el-tooltip>
                </template>
                <el-select
                  :disabled="formDisable && formAuth.indexOf('storageCond') == -1"
                  v-model="skuForm.storageCond"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    v-for="item in $store.getters.selectOptions.storageCondOptions"
                    :key="'key_storageCondOptions_' + item.id"
                    :label="item.dictName"
                    :value="item.id"
                    :disabled="!item.isValid"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!--贮藏-->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                label="贮藏"
                prop="storage"
                ref="storage"
                :class="{
                  'is-change': changeList.includes('storage') || coorectionType['storage'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['storage']"
                    :disabled="!coorectionType['storage']"
                    placement="top-start"
                  >
                    <span>贮藏</span>
                  </el-tooltip>
                </template>
                <el-input
                  v-model="skuForm.storage"
                  maxlength="50"
                  size="small"
                  :disabled="checkOperatePermission('storage') || formDisable"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <!-- 处方分类 -->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                label="处方分类"
                prop="prescriptionCategory"
                :class="{
                  'is-change': changeList.includes('prescriptionCategory') || coorectionType['prescriptionCategory'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['prescriptionCategory']"
                    :disabled="!coorectionType['prescriptionCategory']"
                    placement="top-start"
                  >
                    <span>处方分类</span>
                  </el-tooltip>
                </template>

                <el-select v-model="skuForm.prescriptionCategory" placeholder="请选择" size="small" 
                                  :disabled="
                    (spuCategory.type != 'GENERAL_MEDICINE' ||
                    checkOperatePermission('prescriptionCategory')
                    || formDisable)
                    && formAuth.indexOf('prescriptionCategory') == -1"
                  >
                  <el-option
                    v-for="item in $store.getters.selectOptions.prescriptionCategoryOptions"
                    :key="'key_packageUnitOptions_' + item.id"
                    :label="item.dictName"
                    :value="item.id"
                    :disabled="!item.isValid"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 有效期 -->
            <el-col :md="8" :sm="12" :xs="24" class="reset-input-with-select">
              <el-form-item
                label="有效期"
                prop="validity"
                :class="{
                  'is-change': changeList.includes('validity') || coorectionType['validity'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['validity']"
                    :disabled="!coorectionType['validity']"
                    placement="top-start"
                  >
                    <span>有效期</span>
                  </el-tooltip>
                </template>
                <el-input
                  type="text"
                  v-model="skuForm.validity"
                  :disabled="(checkOperatePermission('validity') || formDisable) && formAuth.indexOf('validity') == -1"
                  size="small"
                >
                  <el-select v-model="skuForm.validityUnit" disabled slot="append" size="small">
                    <el-option label="月" :value="2"></el-option>
                  </el-select>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 质量标准 -->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                :label="chekTitleType(spuCategory.type)"
                prop="qualityStandard"
                :class="{
                  'is-change': changeList.includes('qualityStandard') || coorectionType['qualityStandard'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['qualityStandard']"
                    :disabled="!coorectionType['qualityStandard']"
                    placement="top-start"
                  >
                    <span>{{chekTitleType(spuCategory.type)}}</span>
                  </el-tooltip>
                </template>
                <div class="qualityStandard-container">
                  <div class="qualityStandard-input">
                    <el-input
                      v-model="skuForm.qualityStandard"
                      maxlength="255"
                      :disabled="(operationType == 'operate' || formDisable) && formAuth.indexOf('qualityStandard') == -1"
                      size="small"
                    >
                    </el-input>
                  </div>
                  <div class="copy-btn">
                    <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.qualityStandard)">复制</span>
                  </div>
                  <!-- <div>
                    <el-button style="margin-left: 10px;" type="primary" size="small" @click="addQualityStandard" v-if="spuCategory.type === 'TRADITIONAL_MEDICINE'">
                        <i class="el-icon-plus"></i>
                    </el-button>
                  </div> -->
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <!-- 是否委托生产 -->
            <el-col :md="8" :sm="12" :xs="24">
              <el-form-item
                label="是否委托生产"
                prop="delegationProduct"
                :class="{
                  'is-change': changeList.includes('delegationProduct') || coorectionType['delegationProduct'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['delegationProduct']"
                    :disabled="!coorectionType['delegationProduct']"
                    placement="top-start"
                  >
                    <span>是否委托生产</span>
                  </el-tooltip>
                </template>
                <el-radio-group
                  v-model="skuForm.delegationProduct"
                  :disabled="checkOperatePermission('delegationProduct') || formDisable"
                  size="small"
                >
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <!-- 委托生产厂家 -->
            <el-col :md="8" :sm="24" :xs="24" v-show="skuFormRules.entrustedManufacturer[0].required">
              <el-form-item
                label="受托生产厂家"
                prop="entrustedManufacturer"
                :class="{
                  'is-change': changeList.includes('entrustedManufacturer') || coorectionType['entrustedManufacturer'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['entrustedManufacturer']"
                    :disabled="!coorectionType['entrustedManufacturer']"
                    placement="top-start"
                  >
                    <span>受托生产厂家</span>
                  </el-tooltip>
                </template>
                <div class="copy-input-container">
                  <div class="copy-input">
                    <el-select
                      v-model="skuForm.entrustedManufacturer"
                      placeholder="请选择"
                      filterable
                      :filter-method="searchManufacturer"
                      :disabled="checkOperatePermission('entrustedManufacturer') || formDisable"
                      size="small"
                    >
                      <el-option
                        v-for="item in entrustedManufacturerOptions"
                        :key="'key_packageUnitOptions_' + item.id"
                        :label="item.dictName"
                        :value="item.id"
                        :disabled="!item.isValid"
                      ></el-option>
                    </el-select>
                  </div>
                  <div class="copy-btn">
                    <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.entrustedManufacturerName)">复制</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
             <!-- 受托生产厂家地址 -->
             <el-col :md="8" :sm="24" :xs="24" v-show="skuFormRules.delegationProductAddress[0].required">
              <el-form-item
                label="受托生产厂家地址"
                prop="delegationProductAddress"
                :class="{
                  'is-change': changeList.includes('delegationProductAddress') || coorectionType['delegationProductAddress'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['delegationProductAddress']"
                    :disabled="!coorectionType['delegationProductAddress']"
                    placement="top-start"
                  >
                    <span>受托生产厂家地址</span>
                  </el-tooltip>
                </template>
                <div class="copy-input-container">
                  <div class="copy-input">
                    <el-input
                      v-model="skuForm.delegationProductAddress"
                      maxlength="255"
                      :disabled="checkOperatePermission('delegationProductAddress') || formDisable"
                      size="small"
                    >
                    </el-input>
                  </div>
                  <div class="copy-btn">
                    <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.delegationProductAddress)">复制</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <!--产地-->
            <el-col :md="8" :sm="24" :xs="24" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'">
              <el-form-item prop="originPlace" ref="originPlace">
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['originPlace']"
                    :disabled="!coorectionType['originPlace']"
                    placement="top-start"
                  >
                    <span>产地</span>
                  </el-tooltip>
                </template>
                <el-input
                  v-model="skuForm.originPlace"
                  maxlength="10"
                  :disabled="checkOperatePermission('originPlace') || formDisable"
                  size="small"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- 别名 - 中药 -->
            <el-col :md="8" :sm="12" :xs="24" v-show="spuCategory.type == 'TRADITIONAL_MEDICINE'">
              <el-form-item
                label="别名"
                prop="alias"
                :class="{
                  'is-change': changeList.includes('alias') || coorectionType['alias'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['alias']"
                    :disabled="!coorectionType['alias']"
                    placement="top-start"
                  >
                    <span>别名</span>
                  </el-tooltip>
                </template>
                <el-input v-model="skuForm.alias" size="small" :disabled="checkOperatePermission('alias') || formDisable"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex">
            <!-- 包装条码 -->
            <el-col :md="8" :sm="12" :xs="24" class="packageCode-wrap">
              <div class="packageCode-title">包装条码</div>
              <!-- 小包装条码开关 -->
              <el-form-item
                label-width="150px"
                label="是否填写包装条码"
                prop="noSmallPackageCode"
                :class="{
                  'is-change': changeList.includes('noSmallPackageCode') || coorectionType['noSmallPackageCode'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['noSmallPackageCode']"
                    :disabled="!coorectionType['noSmallPackageCode']"
                    placement="top-start"
                  >
                    <span>是否填写包装条码</span>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="skuForm.noSmallPackageCode" :disabled="checkOperatePermission('noSmallPackageCode') || formDisable">
                  <el-radio :label="0">是</el-radio>
                  <el-radio :label="1">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <!--小包装条码-->
            <el-col :md="8" :sm="12" :xs="24" class="reset-packageBarcode" v-show="!skuForm.noSmallPackageCode ? true : false">
              <el-form-item
                label="小包装条码"
                prop="smallPackageCodeList"
                ref="smallPackageCodeList"
                :class="{
                  'is-change': changeList.includes('smallPackageCodeList') || coorectionType['smallPackageCodeList'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['smallPackageCodeList']"
                    :disabled="!coorectionType['smallPackageCodeList']"
                    placement="top-start"
                  >
                    <span>小包装条码</span>
                  </el-tooltip>
                </template>
                <div class="copy-input-container">
                  <div class="copy-input">
                    <multiple-input
                      @clearValidate="clearValidateSmallPackageBarcode"
                      v-model="skuForm.smallPackageCodeList"
                      :min="true"
                      :validate="skuForm.noSmallPackageCode ? false : true"
                      
                      :disabled="checkOperatePermission('smallPackageCodeList') || formDisable"
                    ></multiple-input>
                  </div>
                  <div class="copy-btn">
                    <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(skuForm.smallPackageCodeList.join(','))">复制</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <!-- 本位码 -->
            <el-col :md="8" :sm="12" :xs="24" v-show="spuCategory.type == 'GENERAL_MEDICINE'">
              <el-form-item
                label="本位码"
                prop="standardCodes"
                ref="standardCodes"
                :class="{
                  'is-change': changeList.includes('standardCodes') || coorectionType['standardCodes'],
                }"
              >
                <template v-slot:label>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="coorectionType['standardCodes']"
                    :disabled="!coorectionType['standardCodes']"
                    placement="top-start"
                  >
                    <span>本位码</span>
                  </el-tooltip>
                </template>
                <el-input
                  v-model="skuForm.standardCodes"
                  maxlength="14"
                  size="small"
                  oninput="value=value.replace(/[^\d]/g,'')"
                  :disabled="checkOperatePermission('standardCodes') || formDisable"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 外包装图片 -->
          <div class="image-box2">
            <div class="img-title">
              外包装图片
              <br /><span style="color: #bababa">(最多60张)</span>
            </div>
            <div class="img-content">
              <ImageUploadWithOCR
                :preview="true"
                :fileList="skuForm.outPackageImgList"
                :limit="60"
                :uploadUrl="ImageUploadUrl"
                :enableOCR="$route.query.procKey  == '新品上报流程' ? true : false"
                :disabled="formDisable"
                @change="outPackageImgUploadSuccess"
              ></ImageUploadWithOCR>
            </div>
          </div>
          <div class="image-box2">
            <div class="img-title">
              说明书图片
              <br /><span style="color: #bababa">(最多60张)</span>
            </div>
            <div class="img-content">
              <ImageUploadWithOCR
                :preview="true"
                :fileList="skuForm.directionImgList"
                :limit="60"
                :uploadUrl="ImageUploadUrl"
                :enableOCR="$route.query.procKey  == '新品上报流程' ? true : false"
                :disabled="formDisable"
                @change="directionImgUploadSuccess"
                size="small"
              ></ImageUploadWithOCR>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <div v-show="showForm && isGIFT && $route.query.page != 'spu'" style="padding: 0 40px 80px">
      <el-form ref="skuRef2" :model="skuForm" :rules="skuFormRules" class="sku-form">
        <el-row class="image-box">
          <el-col :span="3" style="max-width: 100px">外包装图片<br /><span style="color: #bababa">(最多60张)</span></el-col>
          <el-col :span="21" style="text-align: left">
            <ImageUploadWithOCR
              :preview="true"
              :disabled="operationType == 'detail' || operationType == 'auditLevel1' || formDisable"
              :fileList="skuForm.outPackageImgList"
              :limit="60"
              :uploadUrl="ImageUploadUrl"
              :enableOCR="$route.query.procKey  == '新品上报流程' ? true : false"
              @change="outPackageImgUploadSuccess"
            ></ImageUploadWithOCR>
          </el-col>
        </el-row>
        <el-row class="image-box">
          <el-col :span="3" style="max-width: 100px">说明书图片<br /><span style="color: #bababa">(最多60张)</span></el-col>
          <el-col :span="21" style="text-align: left">
            <ImageUploadWithOCR
              :preview="true"
              :disabled="operationType == 'detail' || operationType == 'auditLevel1' || formDisable"
              :fileList="skuForm.directionImgList"
              :limit="60"
              :uploadUrl="ImageUploadUrl"
              :enableOCR="$route.query.procKey  == '新品上报流程' ? true : false"
              @change="directionImgUploadSuccess"
            ></ImageUploadWithOCR>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div v-if="$route.query.page == 'spu'" style="padding: 0 40px">
      <sku-table :skuTableData="skuTableData"></sku-table>
    </div>
    <div v-if="!isGIFT" class="partition-content" style="height: 20px; background: #f0f2f5; padding-left: -20px"></div>
    <el-dialog
      title="关联sku"
      width="1000px"
      :visible.sync="skuDialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      @close="skuDialogClose"
    >
      <sku-table :skuTableData="skuData" @copySku="copySku"></sku-table>
    </el-dialog>
    <imagePreview :on-close="closeImageViewer" v-if="imgPreview" :url-list="imgPreviewList"></imagePreview>
    <addQualityStandard ref="addQualityStandard" @saveDict="saveDict"></addQualityStandard>
  </div>
</template>

<script>
import addQualityStandard from "./components/addDict.vue"
import { dictSearchTypeAndName, dictListSearch } from "@/api/dict"
import { sortSku } from "@/api/product"
import uploadImg from "@/components/uploadImg/index"
import ImageUploadWithOCR from "./components/ImageUploadWithOCR.vue"
import multipleInput from "@/components/common/multipleInput"
import skuTable from "./components/skuTable"
import { skuFormRules, skuForm } from "@/utils/basicInfoData"
import { findIdByOptions, findNameByOptions, toCDB } from "@/utils/index.js"
import imagePreview from "@/components/common/preview/imagePreview"
import pinTo from "@/utils/pinTo.js"
export default {
  name: "sku",
  components: {
    multipleInput,
    uploadImg,
    ImageUploadWithOCR,
    imagePreview,
    skuTable,
    addQualityStandard
  },
  filters: {},
  props: {
    // 表单编辑权限
    formAuth: {
      type: Array,
      required: false,
      default: () => [],
    },
    formDisable: {
      type: Boolean,
      default: false,
    },
    /**
     * sau数据
     */
    sauData: {
      type: Array,
      required: false,
      default: () => [],
    },
    /**
     * sku渲染数据
     */
    skuData: {
      type: Array,
      required: false,
      default: () => [],
    },
    // 商品修改改变字段
    changeList: {
      type: Array,
      required: false,
      default: () => [],
    },
    coorection: {
      type: Object,
      required: false,
      default: () => {},
    },
    disableStatus: {
      type: Boolean,
      default: false,
    },
    loadForm: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      showForm: true,
      rowIndex: 0,
      imgPreviewList: [],
      imgPreview: false,
      skuLoading: true, // true: 加载中。false:加载完成
      // SKU 表格数据
      skuTableData: [],
      skuDialogVisible: false,
      //sku表单对象
      skuForm,
      // 表单校验
      skuFormRules,
      skuFormOptions: {
        netContent: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const { netContent, netContentUnit } = this.skuForm;
              if (netContent && !netContentUnit) {
                callback(new Error('请选择净含量单位'));
              }else if (!netContent && netContentUnit) {
                callback(new Error('请输入净含量'));
              }else {
                if(netContent && netContentUnit) {
                  if (!/^\d+(\.\d+)?$/.test(value)) {
                    callback(new Error('请输入有效的数字'));
                  } else if (parseFloat(value) <= 0) {
                    callback(new Error('净含量必须大于0'));
                  } else if (parseFloat(value) > 999999) {
                    callback(new Error('净含量超出范围'));
                  } else {
                    callback();
                  }
                }else {
                  callback();
                }
              }
            },
            trigger: ['blur', 'change']
          }
        ],
        netContentUnit: [
          {
            validator: (rule, value, callback) => {
              const { netContent, netContentUnit } = this.skuForm;
              // 如果单位有值但净含量没有值
              if (netContentUnit && !netContent) {
                callback(new Error('请输入净含量'));
              }
              // 如果净含量有值但单位没有值
              else if (netContent && !netContentUnit) {
                callback(new Error('请选择净含量单位'));
              }
              else {
                callback();
              }
            },
            trigger: ['blur', 'change']
          }
        ]
      },
      // 委托生产厂家选项
      entrustedManufacturerOptions: this.$store.getters.selectOptions.distManufacturerOptions,
      // 图片上传路径
      ImageUploadUrl: process.env.VUE_APP_BASE_API + "/api/file/upload/fileAndName",
      addSkuNum: 0,
      isGIFT: false,
      skuFormAttr: {
        prescriptionCategoryDisable: false,
      },
      sortLoading: false,
      checkRowData: {},
      // 是否展示净含量
      showUnitFirstClass: this.$store.getters.unitTargetFirstCategory,
      // 包装单位label
      unitLabel: "",
      // 净含量单位label
      unitNetContentUnitLabel: "",
      // 净含量枚举
      netContentUnitList: [
        {id: 'g'},{id: 'kg'},{id: '袋'},{id: '罐'},
        {id: '盒'},{id: '条'},{id: '个'},{id: '支'},
        {id: '对'},{id: '饼'},{id: '管'},{id: '板'},
        {id: '片'},{id: '段'},{id: '只'},{id: '带'},
        {id: '件'}
      ],
      isNetContent: false,
    }
  },
  computed: {

    spuBusinessScopeListType: function () {
      return this.$store.getters.spuBusinessScopeListType
    },

    spuApprovalNo: function(){
        return this.$store.getters.spuApprovalNo
    },
    
    spuCategory: function () {
      return this.$store.getters.spuCategory
    },
    // 商品操作类型
    operationType: function () {
      // 驳回修改SKU 以 修改处理
      if (this.$store.getters.operationType == "RejectEdit") {
        return "edit"
      } else {
        return this.$store.getters.operationType
      }
    },
    coorectionType: function () {
      return this.coorection ? this.coorection : {}
    },
    unitTargetFirstCategory: function () {
      return this.$store.getters.unitTargetFirstCategory
    },
    unitZSYYFirstCategory: function () {
      return this.$store.getters.unitZSYYFirstCategory
    },
    // 新增：复制按钮显示条件
    shouldShowCopyButton() {
      // 主要条件：operationType为detail或edit
      if (this.operationType === 'detail' || this.operationType === 'edit') {
        return true;
      }
      
      // 备用条件：根据URL参数判断
      const urlParam = this.$route.query;
      
      // 如果是纠错类型或上架类型，显示复制按钮（这些通常是detail模式）
      if (urlParam.type === 'coorection' || urlParam.type === 'shelves') {
        return true;
      }
      
      // 如果URL中没有明确的新增标识，且有productCode，可能是编辑或详情模式
      if (urlParam.productCode && !urlParam.isAdd) {
        return true;
      }

      if(urlParam.procKey === '新品上报流程'){
        return true;
      }
      
      return false;
    }
  },
  watch: {
    operationType: {
      handler: function (newValue) {
        this.changeColumnVisible(newValue)
      },
    },
    skuTableData: {
      handler: function (newValue, oldValue) {
        if (!this.skuLoading && this.spuState) {
          this.diffSkuTableData(newValue)
        }
      },
      deep: true,
    },
    skuForm: {
      handler: function (newValue, oldValue) {
        this.$store.commit("product/SET_SKUFORM", newValue)
        if (!this.skuLoading) {
          this.findDifferentKeyOfsku(newValue)
        }
      },
      deep: true,
    },
    // 无小包装条码逻辑处理;
    "skuForm.noSmallPackageCode": function (val, oldValue) {
      if (val) {
        this.skuForm.smallPackageCodeList = []
        this.skuFormRules.smallPackageCodeList[0].required = false
      } else {
        this.skuFormRules.smallPackageCodeList[0].required = true
      }
    },
    // 有效期单位转换, 规则：-1 对应 - ;0 对应 *;
    // "skuForm.validity": function (val, oldValue) {
    //   console.log(val);
    //   try {
    //     if (/^(-|\*)?$|^[1-9]{1}\d{0,3}$/g.test(val)) {
    //       switch (val) {
    //         case "-":
    //           this.skuForm.validityTransition = -1;
    //           break;
    //         case "*":
    //           this.skuForm.validityTransition = 0;
    //           break;
    //         default:
    //           this.skuForm.validityTransition = val;
    //           break;
    //       }
    //     } else {
    //       this.skuForm.validity = oldValue;
    //     }
    //   } catch (error) {
    //     console.log(error);
    //   }
    // },
    // 是否委托生产
    "skuForm.delegationProduct": function (val) {
      // debugger
      if (val == 0) {
        this.skuForm.entrustedManufacturer = ""
        this.skuForm.entrustedManufacturerName = ""
        this.skuForm.delegationProductAddress = ""
        this.skuFormRules.entrustedManufacturer[0].required = false
        this.skuFormRules.delegationProductAddress[0].required = false
      } else {
        this.skuFormRules.entrustedManufacturer[0].required = true
        this.skuFormRules.delegationProductAddress[0].required = true
      }
    },
    "skuForm.entrustedManufacturer"(e) {
      if (e && typeof e === "string") {
        this.searchManufacturer(e)
      }
    },
    spuCategory: function (val) {
      console.log(val,'qiyu');
      
      // 设置是否为赠品默认值
      this.isGIFT = false
      // 设置处方分类默认值
      this.skuForm.prescriptionCategory = findIdByOptions("无", "dictName", this.$store.getters.selectOptions.prescriptionCategoryOptions)
      this.skuForm.prescriptionCategoryName = "无"
      // 先隐藏别名，后面判断到是中药是再打开
      // this.$refs.skuTable.hideColumn(
      //   this.$refs.skuTable.getColumnByField("alias")
      // );
      // 本位码校验重置为不校验
      this.skuFormRules.standardCodes[0].required = false
      switch (val.type) {
        case "GIFT":
          this.isGIFT = true
          // 赠品图片回显
          if (this._formatSku) {
            this.skuForm.outPackageImgList = this._formatSku[0].outPackageImgList
            this.skuForm.directionImgList = this._formatSku[0].directionImgList
          }
          break
        case "NOT_MEDICINE":
          break
        case "MEDICAL_INSTRUMENT":
          break
        case "TRADITIONAL_MEDICINE":
          // 中药展示别名
          // this.$refs.skuTable.showColumn(
          //   this.$refs.skuTable.getColumnByField("alias")
          // );
          break
        case "GENERAL_MEDICINE":
          // 设置处方分类默认值
          // this.skuForm.prescriptionCategory = findIdByOptions("空", "dictName", this.$store.getters.selectOptions.prescriptionCategoryOptions)
          // this.skuForm.prescriptionCategoryName = "空"

          // this.skuForm.prescriptionCategory = this._formatSku[0].prescriptionCategory // !!!发现待修复报错，报错为 addProduct.vue页面 updatePorduct() 函数 (this.urlParam.type != "followup") 判断不满足所以没查数据，但不知道修复后会不会有问题所以暂存
          this.skuForm.prescriptionCategory = this._formatSku && this._formatSku[0].prescriptionCategory || ''
          this.skuForm.prescriptionCategoryName = ""
          this.skuFormRules.standardCodes[0].required = true
          break
        default:
          break
      }
      //备份一份SKU表单数据方便重置表单
      this.defaultSkuForm = _.cloneDeep(this.skuForm)
    },
    unitTargetFirstCategory: function (val) {
      this.showUnitFirstClass = val
    },
    "skuForm.packageUnit": function (val) {
      this.packageUnitChange(val,'unit')
    },
    "skuForm.spec": function (val) {
      if(this.$store.getters.unitZSYYFirstCategory) {
        this.validateSpec(val)
      }
      this.$bus.$emit('reMatchBySpec', this.skuForm.spec)
      this.$store.commit('product/SET_SPEC', this.skuForm.spec)
    },
    unitZSYYFirstCategory: function (val) {
      if(!val) {
        if(!this.$store.getters.unitTargetFirstCategory) {
          this.skuForm.netContentUnit = this.skuForm.netContent = ''
        }
      }else {
        this.validateSpec(this.skuForm.spec)
      }
    },
    "spuCategory.type": function (val) {
      if(this.$route.query.pageType != 'detail'){
        this.autoFillSkuInfo()
      }
    },
    spuApprovalNo: function (val) {
      // console.log("spuApprovalNo");
      
      if(this.$route.query.pageType != 'detail'){
        this.autoFillSkuInfo()
      }
    },
    spuBusinessScopeListType: function (val) {
      // console.log("spuBusinessScopeListType");
      if(this.$route.query.pageType != 'detail'){
        this.autoFillSkuInfo()
      }
    },
  },
  created() {
    this.init()
  },
  mounted() {
      this.$nextTick(() => {
        this.$store.commit('product/SET_SPEC', this.skuForm.spec)
        this.$bus.$emit('reMatchBySpec', this.skuForm.spec)
      })
    if(this.skuForm.spec){
      this.$nextTick(() => {
        this.$store.commit('product/SET_SPEC', this.skuForm.spec)
      })
    }
  },
  methods: {
    specBlur(){
      this.$store.commit('product/SET_SPEC', this.skuForm.spec)
    },
    copyInfo(info){
      if(info){
        if(navigator.clipboard && navigator.clipboard.writeText){
          navigator.clipboard.writeText(info).then(() => {
            this.$message.success('复制成功')
          })
        }else {
          const textArea = document.createElement('textarea');
          textArea.value = info;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          this.$message.success('复制成功')
        }
      }
    },
    autoFillSkuInfo(){
      // console.log("store", this.$store.state.product)
      // console.log("存储条件", this.$store.getters.selectOptions.storageCondOptions);
      const storageCondOptionsTarget = this.$store.getters.selectOptions.storageCondOptions.find(item => item.dictName == '常温')
      if(this.spuCategory.type == "TRADITIONAL_MEDICINE"){
        this.$set(this.skuForm, 'storageCond', storageCondOptionsTarget.id)
        this.$set(this.skuForm, 'qualityStandard', '')
      }else if(this.spuCategory.type == "MEDICAL_INSTRUMENT"){
        this.$nextTick(() => {
          this.$set(this.skuForm, 'storageCond', storageCondOptionsTarget.id) 
          this.$set(this.skuForm, 'qualityStandard', this.spuApprovalNo)
        })
      }else if(this.spuCategory.type == "NOT_MEDICINE"){
        this.$set(this.skuForm, 'qualityStandard', '')
        if(this.spuApprovalNo.includes('妆')){
          this.$set(this.skuForm, 'storageCond', storageCondOptionsTarget.id)
        }else if (this.spuBusinessScopeListType.includes('non_health_food')){
          this.$set(this.skuForm, 'storageCond', storageCondOptionsTarget.id)
        }else if(this.spuBusinessScopeListType.includes('health_food')){
            this.$set(this.skuForm, 'storageCond', storageCondOptionsTarget.id)
          }else if(this.spuBusinessScopeListType.includes('orther')){
            this.$set(this.skuForm, 'storageCond', storageCondOptionsTarget.id)
          }else if(this.spuBusinessScopeListType.includes('disinfectant_products')){
            this.$set(this.skuForm, 'storageCond', storageCondOptionsTarget.id)
          }
        }else {
          this.$set(this.skuForm, 'qualityStandard', '')
        }
    },
    addQualityStandard(){
      this.$refs.addQualityStandard.open({
        title: '请选择规格型号',
        type: 12,
        dictName: this.skuForm.qualityStandard,
      })
    },
    validateSpec(value) {
      const pattern = /(\d+)(支|瓶)/;  // 精确支单位匹配
      const match = pattern.exec(value);
      // 根据匹配结果处理
      if (match) {
        this.skuForm.netContentUnit = match[2]
        this.skuForm.netContent = match[1]
      }else {
        this.skuForm.netContentUnit = this.skuForm.netContent = ''
      }
    },
    chekTitleType(type){
      return type == 'MEDICAL_INSTRUMENT'?'产品技术要求编号':'质量标准'
    },
    init() {
      // 接收SAU组件的数据变更
      this.$bus.$on("sauChange", (data) => {
        // this.sauState.flag : false: 没有新增的SAU; true:有新增的sau
        this.sauState = data
      })

      // 接收SPU初始化渲染完成状态
      this.$bus.$on("spuLoading", (data) => {
        this.skuData.forEach((item) => {
          if (item.validity == 0) {
            item.validity = "*"
          }
          if (item.validity == -1) {
            item.validity = "-"
          }
        })
        this.spuState = data
        // 根据operationType 处理表格列表的展示逻辑
        if (this.skuData.length > 0) {
          if (this.$route.query.page == "spu") {
            this.formatSku()
            return
          }
          if (this.isGIFT) {
            this.formatSku()
            this.skuForm = this.skuData[0]
            return
          }
          let skuCode
          this.sauData.forEach((item) => {
            if (item.sauCode == this.$route.query.productCode) {
              skuCode = item.skuCode
            }
            return
          })
          if (!this.sauData.length && this.loadForm) {
            this.skuForm = this.skuData[0]
            this.searchManufacturer(this.skuForm.entrustedManufacturerName)
          } else {
            this.skuData.forEach((item) => {
              if (item.skuCode == skuCode) {
                this.skuForm = item
                this.searchManufacturer(this.skuForm.entrustedManufacturerName)
              }
            })
          }
          // 格式化SKU
          this.formatSku()
        } else {
          this.resetSku()
          this.skuLoading = false
          // 备份初始化的SKU表格数据
          this.initializedSkuTableData = _.cloneDeep(this.skuTableData)
          // 通知SAU SKU的加载状态
          this.$bus.$emit("skuLoading", {
            skuHasAdd: false, // 页面初始化渲染完成时是否存在新增的SKU
          })
        }
      })
      this.$bus.$on("reuseEvent", (data) => {
        this._resueSpuCode = data
      })
    },
    widthCheck (str, len) { 
      var temp = 0 
      for (let i = 0; i < str.length; i++) { 
        if (/[\u4e00-\u9fa5]/.test(str[i])) { 
          temp += 2 
        } else {
         temp++ 
        } 
        if (temp > len) { 
          this.skuForm.spec = str.substr(0, i);
          return; 
        } 
      } 
    },
    //添加SKU
    addSku() {
      if (this.addSkuNum > 0) {
        this.$message.error("sku每次只能生成一条")
        return false
      }
      if (this.spuCategory.type == "EMPTY") {
        this.$message.error("请先选择商品分类")
        return false
      }
      // this.sauState.flag : false: 没有新增的SAU; true:有新增的sau
      if (this.sauState && this.sauState.flag) {
        this.$message.error("SKU已增加SAU,不可新增")
        return false
      }
      this.skuDialogVisible = true
      this.$nextTick(() => {
        // 清除SKU弹层组件的表单状态
        this.$refs.skuRef.clearValidate()
      })
    },
    hasStorageCond(){
      if(this.operationType === 'present'){
        return ''
      }else{
        return 'storageCond'
      }
    },
    // this.checkRowData :当前选中的表单行数据
    editSku() {
      if (!this.checkRowData) {
        this.$message.error("请选择一行修改")
        return false
      }
      if (this.spuCategory.type == "EMPTY" && this.operationType == "update") {
        this.$message.error("请先选择商品分类")
        return false
      }
      switch (
        this.operationType //根据操作类型处理对应逻辑
      ) {
        case "edit":
          // 商品修改页面的修改  && 存在多条SKU;判断只可修改参数传入的SKU
          if (this.$route.query.productCode && this.$route.query.productCode != this.checkRowData.row.skuCode) {
            this.$message.error("此SKU不可修改")
            return false
          }
          this.editSkuForEdit()
          break
        case "auditLevel1":
          this.$message.error("运营中心审核不可修改")
          return false
        case "auditLevel3":
          this.$message.error("财务部审核不可修改")
          return false
        case "auditLevel2":
          this.editSkuAuditLevel2()
          return false
        // 预首营处理逻辑等同于二审
        case "operate":
          this.editSkuAuditLevel2()
          return false
          break
        default:
          this.editSkuDefault()
          break
      }
    },
    // 常规处理商品时的修改SKU逻辑
    editSkuDefault() {
      if (this.operationType != "present" && this.checkRowData.row.productId) {
        this.$message.error("此SKU不可修改")
        return false
      }
      // 添加修改标识
      this.editState = true
      this.formatSkuforTable("edit")
    },
    //修改商品时的修改SKU逻辑
    editSkuForEdit() {
      if (this.$route.query.productType != "2") {
        this.$message.error("此SKU不可修改")
        return false
      } else {
        // 添加修改标识
        this.editState = true
        this.formatSkuforTable("edit")
      }
    },
    // 二审 || 预首营时，修改商品逻辑
    editSkuAuditLevel2() {
      // 二审 || 预首营时  && 存在多条SKU;判断只可修改参数传入的SKU
      if (this.$route.query.productCode && this.$route.query.productCode != this.checkRowData.row.skuCode) {
        this.$message.error("此SKU不可修改")
        return false
      }
      // 添加修改标识
      this.editState = true
      this.formatSkuforTable("edit")
    },
    // this.checkRowData :当前选中的表单行数据
    copySku(row) {
      this.formatSkuforTable(row, "copy")
      this.skuDialogVisible = false
    },
    formatSkuforTable(checkRowData, type) {
      let data = _.cloneDeep(checkRowData)
      if (type == "copy") {
        //复制行时，清空 sku编码,商品ID,自营状态
        data.skuCode = ""
        data.productId = ""
        data.preOperateStatusName = ""
      }
      for (let key in this.skuForm) {
        if (key == "validity") {
          this.skuForm[key] = this.transformValidity(data[key], "noUnit")
          continue
        }
        if (type == "edit" && this.spuCategory.type != "GENERAL_MEDICINE") {
          // 如果是修改 && 商品分类不是普通药品，则处方分类和名称不再根据表格数据赋值，根据对应的商品分类直接赋值
          if (key == "prescriptionCategory") {
            continue
          }
          if (key == "prescriptionCategoryName") {
            continue
          }
        }
        this.skuForm[key] = data[key]
      }
      // 商品复用标记状态保存
      if (data.spuMultiplex == 1 && type == "edit") {
        this.skuForm.spuMultiplex = 1
      }
      // 委托生产厂商数据
      this.searchManufacturer(this.skuForm.entrustedManufacturerName)
      this.skuForm.validityUnit = 2 // 包装单位目前写死
      this.skuDialogVisible = true
    },
    // this.checkRowData :当前选中的表单行数据
    deleteSku() {
      if (!this.checkRowData) {
        this.$message.error("请选择一行删除")
        return false
      }
      if (this.checkRowData.row.skuCode) {
        this.$message.error("此SKU不可删除")
        return false
      }
      if (this.sauState && this.sauState.flag) {
        this.$message.error("SKU已增加SAU,请先删除sau")
        return false
      }
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.skuTableData.splice(this.rowIndex, 1)
        this.addSkuNum--
        this.checkRowData = null
        this.$message.success("删除成功!")
      })
    },
    /**
     * 保存SKU
     */
    saveSku() {
      let item = _.cloneDeep(this.skuForm)
      // 校验数据是否可以保存
      if (!this.canSave(item)) {
        // 如果为修改SKU
        if (this.editState) {
          if (this.operationType != "present") {
            this.checkRowData = null
            this.skuTableData.splice(this.rowIndex, 1, item)
          } else {
            this.checkRowData.row = item
            this.$set(this.skuTableData, 0, item)
            // this.$refs.skuTable.setRadioRow(this.skuTableData[0]);
          }
          this.editState = null
          // 新增或复制行SKU
        } else {
          this.skuTableData.push(item)
          this.addSkuNum++
          // 重置选中项
          this.$refs.skuTable.clearRadioRow()
        }
        //通知SAU组件数据更新
        this.$bus.$emit("selectedSku", {
          row: {},
          rowIndex: "",
          sauFlag: false, // 通知SAU不可以增加 (修改 || 新增 || 复制行 会默认清除掉SKU选中)
          // reset: false, //不重置sau
          // skuHasAdd:this.addSkuNum > 0 ? true : false // 是否存在新增SKU, true: 存在；false: 不存在
        })
        this.skuDialogVisible = false
      }
    },
    /**
     * @description:是否可以保存当前 SKU（判断是否存在重复）
     * @param {object}  = [sku] 当前添加的sku
     * @return: true：不可以；false: 可以
     */
    canSave(sku) {
      // 初次添加
      if (this.skuTableData.length < 1) {
        return false
      }
      //拷贝当前选中的SKU行，避免污染源数据
      let currentSku = _.cloneDeep(sku)
      // 有效期单位需要取转换后的值
      currentSku.validity = currentSku.validityTransition
      // 备份一份SKU数据
      let cloneSkuTableData = _.cloneDeep(this.skuTableData.filter((item) => item.skuCode != currentSku.skuCode))

      // 判断是否重复
      return cloneSkuTableData.some((oldSku) => {
        let skuCode = oldSku.skuCode
        let currentSkuObj = {},
          oldSkuObj = {}
        currentSkuObj = {
          skuName: currentSku.skuName,
          brand: currentSku.brand,
          spec: currentSku.spec,
          delegationProduct: currentSku.delegationProduct,
          packageUnit: currentSku.packageUnit,
          smallPackageCodeList: currentSku.smallPackageCodeList,
        }
        oldSkuObj = {
          skuName: oldSku.skuName,
          brand: oldSku.brand,
          spec: oldSku.spec,
          delegationProduct: oldSku.delegationProduct,
          packageUnit: oldSku.packageUnit,
          smallPackageCodeList: oldSku.smallPackageCodeList,
        }

        if (currentSku.delegationProduct) {
          currentSkuObj.entrustedManufacturer = currentSku.entrustedManufacturer
          oldSkuObj.entrustedManufacturer = oldSku.entrustedManufacturer
        }

        if (this.spuCategory.type == "TRADITIONAL_MEDICINE") {
          currentSkuObj.originPlace = currentSku.originPlace
          oldSkuObj.originPlace = oldSku.originPlace
        }
        let repeat = _.isEqual(currentSkuObj, oldSkuObj)
        if (repeat) {
          // this.$message.error(`sku已存在不可重复新增，sku编码为${skuCode}`);
          // return true;
        }
      })
    },

    /**
     * @description:对比新旧 SkuTableData 数据
     * @param {array} newVal 最新的值
     */
    diffSkuTableData(newVal) {
      let _newVal = _.cloneDeep(newVal)
      for (let item of _newVal) {
        delete item._XID
      }
      let res = _.isEqual(_newVal, this.initializedSkuTableData)
      if (!this.skuLoading) {
        this.$bus.$emit("productChange", !res)
      }
    },

    // 格式化 props 传入的SKU数据
    async formatSku() {
      // 设置 SKU 为加载中状态
      this.skuLoading = true
      //拷贝一份，避免污染源数据
      this._formatSku = _.cloneDeep(this.skuData)
      // 格式化 同步待跟进数据
      await this.formatSkuForUpdate()
      // 格式化 新品上报数据
      await this.formatSkuForPresent()
      // debugger
      this.resetSku()
      for (let i = 0; i < this._formatSku.length; i++) {
        // 图片路径处理
        let item = this._formatSku[i]
        item.outPackageImgList.forEach((list) => {
          let obj = _.cloneDeep(list)
          list.url = obj.mediaUrl
          list.name = obj.mediaName
        })
        item.directionImgList.forEach((list) => {
          let obj = _.cloneDeep(list)
          list.url = obj.mediaUrl
          list.name = obj.mediaName
        })
        // 赠品时，回显对应的图片数据
        if (this.isGIFT) {
          this.skuForm.outPackageImgList = item.outPackageImgList
          this.skuForm.directionImgList = item.directionImgList
          break
        }
        // 品牌分类名称处理
        if (item.brandCategory) {
          item.brandCategoryName = findNameByOptions(item.brandCategory, "id", this.$store.getters.selectOptions.brandCategoryOptions)
        }
        // 有效期处理
        item.validityTransition = item.validity
        // 别名处理
        item.alias = item.skuAliasText
        
        
        //统一处理字段返回值值为 NULL 时的转换 （ NULL会影响重复判断 ）
        !item.smallPackageCodeList ? (item.smallPackageCodeList = []) : "" // 小包装条码
        !item.mediumPackageCodeList ? (item.mediumPackageCodeList = []) : "" // 中包装条码
        !item.piecePackageCodeList ? (item.piecePackageCodeList = []) : "" // 件包装条码
        !item.entrustedManufacturer ? (item.entrustedManufacturer = "") : "" // 委托生产厂家id
        !item.entrustedManufacturerName ? (item.entrustedManufacturerName = "") : "" // 委托生产厂家
        !item.brandCategory ? (item.brandCategory = "") : "" // 品牌分类

        // 回填表格数据
        let skuObj = {}
        for (let key in this.skuForm) {
          skuObj[key] = item[key]
        }
        // debugger
        // 记录当前SKU的SPU是否为复用 spuMultiplex=1 ：复用
        skuObj.spuMultiplex = item.spuMultiplex
        // 草稿 || 同步待提交 ||新品上报时，计算SKU数量,不显示SKU编码
        if ((this.operationType == "draft" || this.operationType == "update" || this.operationType == "present") && !skuObj.productId) {
          this.addSkuNum++
          skuObj.skuCode = ""
        }
        this.skuTableData.push(skuObj)
        if (this.operationType == "present") {
          this.checkRowData.row = this.skuTableData[0]
        }
      }

      // 如果存在新增的SKU
      if (this.addSkuNum > 0) {
        //通知SAU组件SKU渲染完成
        this.$bus.$emit("skuLoading", {
          skuHasAdd: true, // 页面初始化渲染完成时是否存在新增的SKU
        })
      } else {
        //通知SAU组件SKU渲染完成
        this.$bus.$emit("skuLoading", {
          skuHasAdd: false, // 页面初始化渲染完成时是否存在新增的SKU
        })
      }
      // 备份初始化的SKU
      this.initializedSkuTableData = _.cloneDeep(this.skuTableData)
      await this.$nextTick()
      this.setSkuItemFirst()
      this.skuLoading = false
    },
    /**
     * @description: 设置可编辑的SKU为TABLE第一个
     * @param {type}
     * @return {type}
     */
    setSkuItemFirst() {
      // 如果是商品修改 && SKU修改 && 条数大于1
      if (this.operationType == "edit" && this.$route.query.productCode && this.$route.query.productType == 2 && this.skuTableData.length > 1) {
        // let index=0;
        // for(let i=0;i<this.skuTableData.length;i++){
        //   if(this.skuTableData[i].skuCode==this.$route.query.productCode && i>0){
        //     index=i;
        //   }
        // }
        let _this = this
        let index = this.skuTableData.findIndex((element, index) => {
          if (element.skuCode == _this.$route.query.productCode) {
            return true
          }
        })
        if (index > 0) {
          let itemArr = this.skuTableData.splice(index, 1)
          this.skuTableData.unshift(itemArr[0])
        }
        // 设置第一行默认选中
        // this.$refs.skuTable.setCurrentRow(this.skuTableData[0]);
        // this.$refs.skuTable.setRadioRow(this.skuTableData[0], true);
        // this.cellClickEvent({
        //   row: this.$refs.skuTable.getRadioRecord(),
        //   rowIndex: 0,
        // });
      }
    },
    /**
     * @description:格式化同步待跟进SKU数据
     */
    async formatSkuForUpdate() {
      if (this.operationType != "update") {
        return false
      }
      for (let item of this._formatSku) {
        // 商品名
        item.skuName = item.goodsName
        // 规格
        item.spec = item.specifications
        // 包装单位
        let packingUnitName = item.packingUnit
        item.packingUnit = findIdByOptions(item.packingUnit, "dictName", this.$store.getters.selectOptions.packageUnitOptions)
        item.packingUnit ? (item.packageUnitName = packingUnitName) : (item.packageUnitName = "")
        // 小包装条码
        item.smallPackageCodeList = item.smallPackageBarCode.split(",")
        // item.smallPackageCodeList = [];
        // 有效期
        item.validity = item.periodOfValidity.substring(0, 4)
        // 处方分类
        debugger
        let prescriptionCategoryName = item.prescriptionCategory
        item.prescriptionCategory = findIdByOptions(
          item.prescriptionStr,
          "dictName",
          this.$store.getters.selectOptions.prescriptionCategoryOptions
        )
        item.prescriptionCategory ? (item.prescriptionCategoryName = prescriptionCategoryName) : (item.prescriptionCategoryName = "")
        // 外包装图片
        item.outPackageImgList = item.mediaBusinessDtoList ? item.mediaBusinessDtoList : []

        // 设置默认值
        item.mediumPackageCodeList = []
        item.piecePackageCodeList = []
        // item.outPackageImgList = [];
        item.directionImgList = []
        item.delegationProduct = 0
      }
    },
    /**
     * @description: 格式化新品上报数据
     * @param {type}
     * @return:
     */
    formatSkuForPresent() {
      if (this.operationType != "present") {
        return false
      }
      for (let item of this._formatSku) {
        // debugger
        // 商品名称
        // item.skuName=item.productName;
        // 包装单位
        if (!item.spuCode) {
          let packageUnitName = item.packageUnit
          item.packageUnit = findIdByOptions(item.packageUnit, "dictName", this.$store.getters.selectOptions.packageUnitOptions)
          item.packageUnit ? (item.packageUnitName = packageUnitName) : (item.packageUnitName = "")
          // 设置默认值
          // item.mediumPackageCodeList = [];
          // item.piecePackageCodeList = [];
          // item.directionImgList = [];
          item.delegationProduct = 0
          item.noSmallPackageCode = 0
        }
      }
    },
    /**
     * @description: table 行选中SKU时触发
     * @param {object} data 选中行数据对象
     * @param {object} event 事件对象
     * @return:{boolen}
     */
    cellClickEvent(data, event) {
      if (this.operationType == "present") return
      this.$store.commit("product/SET_SKUFORM", data.row)
      // 详情时不做任何处理
      if (this.operationType == "detail") {
        return false
      }
      // 保存当前行id
      this.rowIndex = data.rowIndex
      this.checkRowData = data
      // 以下处理逻辑详见：assets/流程图/SKU,SAU 流程梳理图
      // 判断是否已经新增sau
      if (this.sauState && this.sauState.flag) {
        // 判断当前选中行是否为新增SAU关联行
        this.currentRowIsNewSauRelevancy()
      } else {
        // 如果当前整个SKU是否存在新增行
        if (this.addSkuNum > 0) {
          //如果存在
          // 当前行是否为新增行
          this.currentRowIsNewRow()
        } else {
          //通知SAU组件数据更新
          this.$bus.$emit("selectedSku", {
            row: data.row,
            rowIndex: data.rowIndex,
            sauFlag: true, // 通知SAU可以增加
            // reset: false, //不重置sau
            // skuHasAdd:this.addSkuNum > 0 ? true : false // 是否存在新增SKU, true: 存在；false: 不存在
          })
        }
      }
    },
    /**
     * @description: 判断当前选中行是否为新增 SAU关联行
     * @return:
     */
    currentRowIsNewSauRelevancy() {
      let currentRow = this.checkRowData.row
      // 如果为新增关联行
      if (currentRow.skuCode == this.sauState.skuCode) {
        //通知SAU组件数据更新
        this.$bus.$emit("selectedSku", {
          row: currentRow,
          rowIndex: this.checkRowData.rowIndex,
          sauFlag: true, // 通知SAU可以增加
          // reset: false, //不重置sau
          // skuHasAdd:this.addSkuNum > 0 ? true : false // 是否存在新增SKU, true: 存在；false: 不存在
        })
      } else {
        this.$message.error("已有修改的SKU,此SKU不可修改")
        //通知SAU组件数据更新
        this.$bus.$emit("selectedSku", {
          row: currentRow,
          rowIndex: this.checkRowData.rowIndex,
          sauFlag: false, //不可增加sau，
          // reset: false, //不重置sau
          // skuHasAdd:this.addSkuNum > 0 ? true : false // 是否存在新增SKU, true: 存在；false: 不存在
        })
      }
    },
    /**
     * @description: 当前行是否为新增行
     * @return:
     */
    currentRowIsNewRow() {
      let currentRow = this.checkRowData.row
      if (!currentRow.skuCode) {
        //通知SAU组件数据更新
        this.$bus.$emit("selectedSku", {
          row: currentRow,
          rowIndex: this.checkRowData.rowIndex,
          sauFlag: true, // 通知SAU可以增加
          // reset: false, //不重置sau
          // skuHasAdd:this.addSkuNum > 0 ? true : false // 是否存在新增SKU, true: 存在；false: 不存在
        })
      } else {
        this.$message.error("已有新增的SKU,此SKU不可修改")
        //通知SAU组件数据更新
        this.$bus.$emit("selectedSku", {
          row: currentRow,
          rowIndex: this.checkRowData.rowIndex,
          sauFlag: false, //不可增加sau，
          // reset: false, //不重置sau
          // skuHasAdd:this.addSkuNum > 0 ? true : false // 是否存在新增SKU, true: 存在；false: 不存在
        })
      }
    },
    /**
     * @description: 判断预首营修改时的 FORM 字段编辑权限
     * @param {string} skuFormRulesKey skuFormRules 对象的 key
     * @return:
     */
    checkOperatePermission(skuFormRulesKey) {
      // if (this.operationType == "operate") {
      //   return this.skuFormRules[skuFormRulesKey][0].required;
      // } else {
      //   // 不是预首营时，小包装条码额外判断
      //   if (skuFormRulesKey == "smallPackageCodeList") {
      //     return this.skuForm.noSmallPackageCode;
      //   }
      //   return false;
      // }
      if (this.operationType == "detail") {
        return true
      }
    },
    // 处理图片接口结构
    handleImgData(list) {
      let flag = true
      let arr = []
      for (let item of list) {
        // 防止图片未上传完成
        if (item.status == "uploading") {
          this.$message.error("图片上传中，请稍后再试...")
          flag = false
          break
        }
        if (item.status == "success") {
          if (item.response) {
            let { mediaName, mediaUrl, meidiaType } = item.response.data
            arr.push({ mediaName, mediaUrl, meidiaType: 0 })
          } else {
            arr.push({
              mediaName: item.mediaName ? item.mediaName : item.name,
              mediaUrl: item.mediaUrl ? item.mediaUrl : item.url,
              meidiaType: 0, // 媒体类型(0:图片, 1:视频)
            })
          }
          flag = true
        }
      }
      return { flag, arr }
    },
    /**
     * @description: 外包装图片上传成功
     * @param {array} imageList 上传成功时的图片数组
     * @return:
     */
    outPackageImgUploadSuccess(imageList) {
      this._outPackageImgList = imageList
      let { flag, arr } = this.handleImgData(imageList)
      if (!flag) return
      this.skuForm.outPackageImgList = arr
      // 如果是赠品，则触发通知页面修改状态
      if (this.isGIFT) {
        this.$bus.$emit("productChange", true)
      }
    },
    /**
     * @description: 说明书图片上传成功
     * @param {array} imageList 上传成功时的图片数组
     * @return:
     */
    directionImgUploadSuccess(imageList) {
      this._directionImgList = imageList
      let { flag, arr } = this.handleImgData(imageList)
      if (!flag) return
      this.skuForm.directionImgList = arr
      // 如果是赠品，则触发通知页面修改状态
      if (this.isGIFT) {
        this.$bus.$emit("productChange", true)
      }
    },
    /**
     * @description:检测图片是否上传完成
     * @return: true:上传完成，false:上传中
     */
    checkImageIsUpload() {
      let isUpLoad = false
      let imgList = []
      let noImage = 0
      if (Array.isArray(this._outPackageImgList)) {
        imgList = [...imgList, ...this._outPackageImgList]
      } else {
        // 没有图片，或图片再上传中
        noImage++
      }
      if (Array.isArray(this._directionImgList)) {
        imgList = [...imgList, ...this._directionImgList]
      } else {
        // 没有图片，或图片再上传中
        noImage++
      }
      if (noImage < 2) {
        //存在上传图片
        isUpLoad = true
        for (let item of imgList) {
          if (item.status != "success") {
            isUpLoad = false
            break
          }
          if (item.hasOwnProperty("response") && item.response.retCode == 0) {
            item.mediaUrl = item.response.data.mediaUrl
            item.mediaName = item.response.data.mediaName
            item.meidiaType = 0
            delete item.raw
            delete item.name
            delete item.percentage
            delete item.response
            delete item.size
            // 这是三个属性不能清除，上传组件内部在使用
            // delete item.status;
            // delete item.uid;
            // delete item.url;
          }
        }
      }
      if (noImage == 2) {
        //打开上传弹层什么也不做时
        return true
      }
      if (isUpLoad) {
        this._outPackageImgList ? (this.skuForm.outPackageImgList = this._outPackageImgList) : ""
        console.log(this.skuForm.outPackageImgList)
        this._directionImgList ? (this.skuForm.directionImgList = this._directionImgList) : ""
      } else {
        this.$message.error("图片上传中，请耐心等待")
      }
      return isUpLoad
    },
    // 清除小包装条码校验
    clearValidateSmallPackageBarcode() {
      this.$refs.smallPackageCodeList.clearValidate()
    },
    /**
     * @description: 转换表格有效期显示内容
     * @param {string} validity 有效期
     * @param {any} unit :true 不需要单位, false 需要单位
     */
    transformValidity(validity, unit) {
      // -1 对应 - ;0 对应 *;
      switch (validity) {
        case -1:
          return "-"
          break
        case 0:
          return "*"
          break
        default:
          if (unit) {
            return validity
          } else {
            return validity + "月"
          }
          break
      }
    },
    /**
     * @description: 模糊查询生产厂家
     * @param {string} inputValue 查询文本
     * @return: undefined
     */
    async searchManufacturer(inputValue) {
      // console.log("1111")
      let entrustedManufacturer = await dictSearchTypeAndName({
        dictName: inputValue ? inputValue : "药",
        type: 12,
      })
      this.entrustedManufacturerOptions = entrustedManufacturer.list
    },
    /**
     * @description: 预览图片
     * @param {arry} list 图片数组
     * @return: undefined
     */
    showImagePreview(list) {
      if (list.length < 1) {
        return
      }
      this.imgPreviewList = list
      this.imgPreview = true
    },
    closeImageViewer() {
      this.imgPreview = false
    },
    //弹层取消
    closeSku() {
      this.skuDialogVisible = false
    },
    // SKUDialog 关闭回调
    skuDialogClose() {
      // // debugger
      // this.skuForm = _.cloneDeep(this.defaultSkuForm);
      // // 关闭时，需要清除图片的修改存储值，避免保存时判断出错
      // this._directionImgList = null;
      // this._outPackageImgList = null;
      // // 清除SKU弹层组件的表单状态
      // this.$refs.skuRef.resetFields();
    },
    resetSku() {
      // debugger
      if (!this.skuTableData.length) {
        return
      }
      // debugger
      this.skuTableData = []
      // 清除SAU组件通信数据
      this.sauState = null
      // 清除行ID
      this.rowIndex = null
      this.addSkuNum = 0
      //通知SAU组件数据更新
      this.$bus.$emit("selectedSku", {
        row: {},
        rowIndex: "",
        sauFlag: false, //不可增加sau，
        // reset: true, //重置sau
        // skuHasAdd:this.addSkuNum > 0 ? true : false // 是否存在新增SKU, true: 存在；false: 不存在
      })
    },
    /**
     * 父组件获取SKU数据
     * @return {object} skuForm
     * @param {boolen} isDraft true:保存草稿，false :提交
     */
    getSkuData(isDraft) {
      let isValid = true
      if (!isDraft) {
        if (this.isGIFT) {
          this.$refs.skuRef2.validate((valid) => {
            if (!valid || this.canSave(this.skuForm)) isValid = false
          })
        } else {
          this.$refs.skuRef.validate((valid) => {
            if (!valid || this.canSave(this.skuForm)) isValid = false
          })
        }
      }
      if (!isValid) return false
      if (this.isGIFT) {
        // 如果为赠品
        if (this.checkImageIsUpload()) {
          return [this.skuForm]
        } else {
          this.$message.error("图片上传中，请等待~")
          return false
        }
      }
      if (this.operationType == "present") {
        if (this.skuForm.packageUnit) {
        }
      }
      if (this.skuForm.skuAliasText) {
        this.skuForm.skuAliasList = [
          {
            alias: this.skuForm.skuAliasText,
            aliasCode: pinTo.getPinYinFirstCharacter(this.skuForm.skuAliasText),
          },
        ]
      }
      return [this.skuForm]
    },
    // 商品名格式处理
    formatskuName() {
      this.skuForm.skuName = toCDB(this.skuForm.skuName.replace(/\s/g, ""))
    },
    /**
     * sku数据提交前的格式处理
     * @return {object} formatSku 根据提交接口格式化后的数据
     */
    formatSkuForSubmit() {
      let formatSku = _.cloneDeep(this.skuTableData)
      if (formatSku.length < 1) {
        return []
      }
      for (let skuItem of formatSku) {
        // 如果是复用 || (复用保存草稿)，需要增加添加复用标记处理
        if (this.operationType == "reuse" || (this.operationType == "draft" && this.$route.query.spuCode)) {
          if (skuItem.skuCode || skuItem.productId) {
            // 判断如果存在新增SAU的情况下，获取当前新增的SAU关联的是那个SKU
            this.checkNewSau(skuItem)
          } else {
            // 设置此SKU为复用新增SKU
            skuItem.spuMultiplex = 1
            // 判断如果存在新增SAU的情况下，获取当前新增的SAU关联的是那个SKU
            this.checkNewSau(skuItem)
          }
        }
        // 别名处理
        if (skuItem.alias) {
          skuItem.skuAliasList = [
            {
              alias: skuItem.alias,
              aliasCode: pinTo.getPinYinFirstCharacter(skuItem.alias),
            },
          ]
        } else {
          delete skuItem.alias
        }
        // 有效期
        skuItem.validity = skuItem.validityTransition
        // 商品名code
        skuItem.skuNameCode = pinTo.getPinYinFirstCharacter(skuItem.skuName).replace(/\s/g, "")
        // 删除多余数据
        delete skuItem._XID
        delete skuItem.sauFlag
        delete skuItem.preOperateStatusName //自营状态
        delete skuItem.validityTransition
      }
      formatSku[0].changeList = this.changeList
      return formatSku
    },
    /**
     * @description: 判断当前SKU下是否存在新增的SAU
     * @param {object} sku 当前处理的SKU
     * @return:
     */
    checkNewSau(sku) {
      // 判断如果存在新增SAU的情况下，获取当前新增的SAU关联的是那个SKU
      if (this.sauState && this.sauState.flag && this.sauState.skuCode == sku.skuCode) {
        // 增加存在新增SAU的标记
        sku.hasNewSau = true
        // 设置此SKU为复用新增SKU
        sku.spuMultiplex = 1
      }
    },
    /**
     * @description: 自定义排序
     * @param {param} { column, property, order, $event }
     * @return:
     */
    async sortChangeEvent(param) {
      if (this.skuData.length < 2) {
        return false
      }
      let queryParam = null
      if (this.operationType == "edit" || this.operationType == "detail") {
        queryParam = {
          applyCode: this.$route.query.applyCode ? this.$route.query.applyCode : "",
          productCode: this.$route.query.spuCode,
          productType: 1,
          orderName: param.field,
          orderMethod: param.order,
        }
      }
      if (["auditLevel1", "auditLevel2", "auditLevel3"].indexOf(this.operationType) != -1 && this.$route.query.approvalProcess == 0) {
        queryParam = {
          applyCode: this.$route.query.applyCode ? this.$route.query.applyCode : "",
          productCode: this.$route.query.spuCode,
          productType: 4,
          orderName: param.field,
          orderMethod: param.order,
        }
      }
      if (this.operationType == "reuse") {
        queryParam = {
          applyCode: this.$route.query.applyCode ? this.$route.query.applyCode : "",
          productCode: this._resueSpuCode,
          productType: 1,
          orderName: param.field,
          orderMethod: param.order,
        }
      }
      if (queryParam) {
        this.sortLoading = true
        let res = await sortSku(queryParam)
        this.skuData = res.data.sku
        this.formatSku()
        this.sortLoading = false
      }
    },
    /**
     * @description: 查找SKU中修改的值的KEY
     * @param {newValue} 当前正在编辑的skuForm表单对象
     * @return:
     */
    findDifferentKeyOfsku(newValue) {
      if (
        this.$route.query.procKey === "商品新增流程" ||
        this.$route.query.procKey === "新品上报流程" ||
        this.operationType == "edit" ||
        this.operationType == "operate" ||
        // 审核及审核驳回修改时需要判断是否为修改商品
        (this.operationType == "auditLevel1" && this.$route.query.approvalProcess == 1) ||
        (this.operationType == "auditLevel2" && this.$route.query.approvalProcess == 1) ||
        // 判断是否为商品上架状态修改
        (this.operationType == "auditLevel2" && this.$route.query.type == "shelves") ||
        (this.operationType == "RejectEdit" && this.$route.query.approvalProcess == 1)
      ) {
        for (let item of this.initializedSkuTableData) {
          if (item.skuCode == newValue.skuCode) {
            for (let key in newValue) {
              // 不判断包含 Name 的key && skuName除外
              if (/Name/g.test(key) && key !== "skuName" && key !== "tasteName" && key !== "sizeName" && key !== "colorName") {
                continue
              }
              let res = _.isEqual(newValue[key], item[key])
              if (!res && !this.changeList.includes(key)) {
                this.changeList.push(key)
              } else if (_.isEqual(this.skuForm[key], this._formatSku[0][key])) {
                this.changeList.remove(key)
              }
            }
          }
        }
      } else {
        return
      }
    },
    /**
     * @description: 根据 operationType 值变更表格的列隐藏还是展示逻辑
     * @param {string}  operationType
     * @return:
     */
    changeColumnVisible(operationType) {
      if (operationType != "detail" && operationType != "spuOperate") {
        // this.$refs.skuTable.showColumn(
        //   this.$refs.skuTable.getColumnByField("radiofield")
        // );
      } else {
        // this.$refs.skuTable.hideColumn(
        //   this.$refs.skuTable.getColumnByField("radiofield")
        // );
      }
    },
    /**
     * @description: 根据参数展示table 中的字段展示值
     * @param {string} val 需要处理的值
     * @return: {string}
     */
    radioText(val) {
      if (val == 1) {
        return "是"
      } else if (val === 0) {
        return "否"
      } else {
        return ""
      }
    },

    checkRadioMethod({ row }) {
      if (this.operationType == "present") {
        return !row.skuCode
      } else {
        return true
      }
    },
    handlMoreSku() {
      this.$nextTick(() => {
        this.skuDialogVisible = true
      })
    },
    packageUnitChange(val,type) {
      if(type === 'unit') {
        this.$store.getters.selectOptions.packageUnitOptions.forEach(item => {
          if(val === item.id) {
            this.unitLabel = item.dictName
          }
        })
      }
    },
    validateNetContentFields() {
      // 触发净含量和净含量单位的验证
      this.$refs.skuForm.validateField('netContent');
      this.$refs.skuForm.validateField('netContentUnit');
    }
  },
}
</script>

<style lang="scss" scoped>
.sku-container {
  width: 100%;
  .sku-title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding-left: 20px;
    position: relative;
    .tip {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 15px;
      color: #3b95a8;
    }
  }
  .info-suffix-icon {
    margin: 12px 0 0 5px;
    color: #77787e;
    cursor: pointer;
  }
  /**
     *表单
     */
  .form-wrap {
    width: 100%;
    padding-right: 40px;
    .el-row {
      flex-wrap: wrap;
      .el-col {
        display: flex;
        justify-content: flex-end;
        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 22px;
          /deep/ {
            .el-form-item__label {
              width: 130px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;
            }
          }
          .el-input {
            width: 100%;
          }
          .el-select {
            width: 100%;
          }
        }
        .is-change {
          /deep/ {
            // 调整修改项lable样式
            .el-form-item__label {
              color: #f56c6c;
              font-weight: bold;
            }
          }
        }
        &.reset-input-with-select {
          /deep/ {
            .el-select .el-input {
              width: 66px;
            }
          }
        }
        &.reset-packageBarcode {
          /deep/ {
            .el-form-item__label {
              align-self: flex-start;
              margin-top: 10px;
            }
          }
        }
      }
    }
    .sku-form {
      padding-bottom: 50px;
    }
    .image-box {
      margin-top: 20px;
      margin-bottom: 10px;
      .el-col {
        justify-content: flex-start;
      }
    }

    .qualityStandard-container{
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-item: center;

      .qualityStandard-input{
        flex: 1;
        margin-right: 10px;
      }
    }
    .copy-input-container{
      width: 100%;
      display: flex;
      align-items: center;
      .copy-input {
        flex: 1;
        margin-right: 10px;
      }
    }
    .copy-btn{
      margin-right: 10px;
    }
  }
  .image-box {
    margin-top: 20px;
  }
  .image-view {
    cursor: pointer;
    color: #62aab9;
  }
  .vxe-body--row.row--current {
    .image-view {
      cursor: pointer;
      color: #fff;
    }
  }
  /deep/ .el-dialog__wrapper .el-dialog {
    top: auto !important;
    left: auto !important;
    transform: none !important;
    margin: 15vh auto 0 !important;
    height: calc(100% - 18vh);
    .el-dialog__body {
      height: 100%;
    }
  }
}
.packageCode-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .packageCode-title {
    width: 130px;
    padding: 0 12px;
    text-align: right;
    font-size: 14px;
  }
  .packageCode-content {
    flex: 1;
    padding: 0 20px;
    background: #f0f2f5;
  }
  /deep/ .el-form-item__content,
  .el-form-item {
    margin: 0 !important;
  }
}
.skuTable-wrap {
  padding-bottom: 50px;
}
.image-box2 {
  display: flex;
  margin-bottom: 20px;
  .img-title {
    width: 130px;
    font-size: 14px;
    text-align: right;
    padding: 0 12px;
    font-weight: bold;
  }
  .img-content {
    flex: 1;
  }
}
.input-with-select {
  display: flex;
  align-items: center;
  
  .el-input {
    margin-right: 0;
    /deep/.el-input__inner {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  .el-form-item {
    margin-bottom: 0px !important;
    /deep/.el-input__inner {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
    }
  }
}

.copy-btn-below{
  color: #4A95A9;
  cursor: pointer;
}
</style>
