<template>
  <div class="container">
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="60%"
      @close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="ruleForm"
      >
        <el-form-item label="字典类型" prop="type">
          <el-select
            v-model="ruleForm.type"
            placeholder="请选择"
            disabled
            clearable
          >
            <el-option
              v-for="item in dictTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="商品大类"
          prop="categoryIds"
          v-if="ruleForm.type == 11"
        >
          <el-select
            v-model="ruleForm.categoryIds"
            placeholder="请选择"
            :disabled="ruleForm.parentId"
            clearable
          >
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.dictName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经营范围" v-if="ruleForm.type == 11">
          <el-cascader
            :key="refresh"
            ref="ownCascader"
            style="width: 100%"
            placeholder="请先选择商品大类"
            v-model="businessScopeList"
            @change="handleChange"
            :disabled="!ruleForm.categoryIds || ruleForm.parentId"
            :options="businessScopeListOptions"
            :show-all-levels="true"
            :props="{
              checkStrictly: true,
              value: 'id',
              label: 'dictName',
            }"
            clearable
          >
            <template slot-scope="{ data }">
              <span>{{ data.dictName }}</span>
            </template>
          </el-cascader>
        </el-form-item>
        <el-form-item label="名称" prop="dictName">
          <el-input v-model="ruleForm.dictName" :disabled="dictNameEdit" clearable>
            <template slot="append">
              <i class="el-icon-edit-outline" style="font-size: 18px; color: #409EFF; cursor: pointer;" @click="dictNameEdit = false"></i>
            </template>
          </el-input>
          <!-- <el-input v-model="ruleForm.dictName" :disabled="ruleForm.type == 12" clearable></el-input> -->
        </el-form-item>
        <el-form-item label="厂家地址" prop="productionAddress" v-if="ruleForm.type == 12">
          <el-input v-model="ruleForm.productionAddress" :disabled="addressEdit" clearable>
            <template slot="append">
              <i class="el-icon-edit-outline" style="font-size: 18px; color: #409EFF; cursor: pointer;" @click="addressEdit = false"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="关键词" prop="mnemonicCode">
          <div class="mnemonicCode-box">
            <span class="mnemonicCode-list">
              <span
                class="box"
                v-for="(item, index) in mnemonicCodeList"
                :key="index"
              >
                <el-input
                  class="mnemonicCode-input"
                  v-model="mnemonicCodeList[index]"
                ></el-input>
                <span
                  class="el-icon-error"
                  @click="deleteMnemonicCode(index)"
                ></span>
                <!-- <el-input
                  class="mnemonicCode-input"
                  v-model="mnemonicCodeList[index]"
                  :disabled="ruleForm.type == 12"
                ></el-input>
                <span
                  class="el-icon-error"
                  v-if="ruleForm.type != 12"
                  @click="deleteMnemonicCode(index)"
                ></span> -->
              </span>
            </span>
            <span class="icon el-icon-plus" @click="addMnemonicCode"></span>
            <!-- <span class="icon el-icon-plus" v-if="ruleForm.type != 12" @click="addMnemonicCode"></span> -->
          </div>
        </el-form-item>
        <el-form-item label="简码" prop="simpleCode" v-if="ruleForm.type == 9">
          <el-input
            v-model="ruleForm.simpleCode"
            placeholder="填写一位数字或字母"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="厂家分类"
          prop="parentId"
          v-if="ruleForm.type == 12"
        >
          <el-select v-model="ruleForm.parentId" placeholder="请选择" clearable>
            <el-option
              v-for="item in parentIdList"
              :key="item.id"
              :label="item.dictName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否停用" prop="isValid">
          <el-radio-group v-model="ruleForm.isValid">
            <el-radio :label="0">停用</el-radio>
            <el-radio :label="1">启用</el-radio>
          </el-radio-group>
          <!-- <el-radio-group
            v-model="ruleForm.isValid"
            :disabled="ruleForm.type == 12"
          >
            <el-radio :label="0">停用</el-radio>
            <el-radio :label="1">启用</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="ruleForm.remark" clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sure">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { dictTypeList } from "@/utils/config.js";
import pinTo from "@/utils/pinTo.js";

import {
  dictListSearch,
  saveDict,
  getTotalDictionaryTree,
} from "@/api/dict.js";
export default {
  name: "",
  props: {},
  data() {
    return {
      title: "新增字典",
      dialogVisible: false,
      refresh: 1,
      dictTypeList: [],
      parentIdList: [],
      categoryList: [],
      mnemonicCodeList: [],
      businessScopeListOptions: [],
      businessScopeList: [],
      ruleForm: {
        isValid: 1,
      },
      rules: {
        type: [{ required: true, message: "字典类型必填", trigger: "blur" }],
        dictName: [{ required: true, message: "名称必填", trigger: "blur" }],
        productionAddress: [{ required: true, message: "厂家地址必填", trigger: "blur" }],
        simpleCode: [{ required: true, message: "简码必填", trigger: "blur" }],
        categoryIds: [
          { required: true, message: "商品大类必填", trigger: "change" },
        ],
      },
      dictNameEdit: true,
      addressEdit: true,
    };
  },
  watch: {
    "ruleForm.categoryIds"(val) {
      this.businessScopeList = [];
      this.changeSpuCategory(val);
    },
  },
  methods: {
    open({ title, row, dictName, type }) {
      this.dictNameEdit = true;
      this.addressEdit = true;
      this.dialogVisible = true;
      this.title = title;
      this.dictTypeList = dictTypeList;
      // if (title == "新增字典") {
      //   this.dictTypeList = this.dictTypeList.filter((item) => {
      //     return item.value != 12;
      //   });
      // }
      this.ruleForm.isValid = 1;
      if (row) {
        this.ruleForm = {
          ...row,
          type: row.type * 1,
          categoryIds: row.categoryIds * 1,
        };
        if (row.keywords) {
          this.mnemonicCodeList = row.keywords.split(",");
        }
      }
      if(!this.ruleForm.id){
        this.addressEdit = false;
        this.dictNameEdit = false;
      }
      this.$set(this.ruleForm, "dictName", dictName)
      this.$set(this.ruleForm, "type", type)
      dictListSearch({
        dictName: "",
        isValid: 1,
        type: 23,
      }).then((res) => {
        if (!res.retCode) {
          this.parentIdList = res.data.list;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
      dictListSearch({
        dictName: "",
        isValid: 1,
        type: 7,
      }).then((res) => {
        if (!res.retCode) {
          this.categoryList = res.data.list;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    handleClose() {
      this.ruleForm = {
        isValid: 1,
      };
      this.mnemonicCodeList = [];
      this.businessScopeList = [];
    },
    // 根据商品大类获取经营范围
    changeSpuCategory(type) {
      getTotalDictionaryTree({
        type: "11",
        parentId: type,
      }).then((res) => {
        this.businessScopeListOptions = res.data;
        if (this.ruleForm.parentId) {
          this.getBusinessScopeList(this.ruleForm.parentId);
        }
      });
    },
    getBusinessScopeList(id) {
      this.filterBusiness(this.businessScopeListOptions, id);
    },
    filterBusiness(arr, id) {
      this.refresh++;
      arr.forEach((item) => {
        if (item.id == id) {
          this.businessScopeList.unshift(id);
          if (item.levelNode == 1) {
            return;
          } else {
            this.filterBusiness(this.businessScopeListOptions, item.parentId);
          }
        } else {
          if (item.children) {
            this.filterBusiness(item.children, id);
          }
        }
      });
    },
    handleChange() {
      this.$refs.ownCascader.dropDownVisible = false;
    },
    addMnemonicCode() {
      if (this.mnemonicCodeList.length == 5) {
        this.$message.warning("助记码最多只能5个");
      } else {
        this.mnemonicCodeList.push("");
      }
    },
    deleteMnemonicCode(index) {
      this.mnemonicCodeList.splice(index, 1);
    },
    sure() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return;
        let keywordsArr = [],
          params = Object.assign({}, this.ruleForm);
        this.mnemonicCodeList.forEach((item, index) => {
          if (item != "") {
            keywordsArr.push(item);
          }
        });
        params.keywords = keywordsArr.join(",");
        params.mnemonicCode = pinTo.getPinYinFirstCharacter(params.dictName);
        if (this.ruleForm.type == 11) {
          if (this.businessScopeList.length != 0) {
            params.parentId = this.businessScopeList[
              this.businessScopeList.length - 1
            ];
          } else {
            params.parentId = this.ruleForm.categoryIds;
          }
        }
        saveDict(params).then((res) => {
          if (!res.retCode) {
            this.dialogVisible = false;
            this.$emit('saveManufacturer', this.ruleForm.productionAddress, this.ruleForm.dictName)
            if (!params.id) {
              this.$emit("refresh", 1);
            } else {
              this.$emit("refresh");
            }
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: res.retMsg,
            });
          }
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.container /deep/ {
  .el-dialog__body {
    height: 300px;
    padding-bottom: 0;
    overflow-y: scroll;
  }
  .el-dialog__footer {
    padding-top: 10px;
  }

  .mnemonicCode-box {
    width: 100%;
    height: 40px;
    display: flex;
    .mnemonicCode-list {
      flex: 1;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      .box {
        display: flex;
        position: relative;
        .mnemonicCode-input {
          height: 30px;
          width: 60px;
          margin: 0 5px;
          display: flex;
          .el-input__inner {
            height: 100%;
          }
        }
        .el-icon-error {
          position: absolute;
          top: -2px;
          right: 0;
        }
      }
    }
    .el-icon-plus {
      height: 100%;
      width: 40px;
      background: #3b95a8;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.ruleForm /deep/ {
  .el-select {
    width: 100%;
  }
}
</style>